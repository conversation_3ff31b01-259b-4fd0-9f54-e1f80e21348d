<?php
declare(strict_types=1);
$mainUrl = 'http://localhost/social/';
session_start();

// Try to load autoloader from different possible locations
$autoloadPaths = [
    __DIR__ . '/vendor/autoload.php',
    __DIR__ . '/../vendor/autoload.php',
    __DIR__ . '/../../vendor/autoload.php'
];

foreach ($autoloadPaths as $path) {
    if (file_exists($path)) {
        require_once $path;
        break;
    }
}

use PhpSanitization\PhpSanitization\Sanitization;
use PhpSanitization\PhpSanitization\Utils;

// Initialize with proper dependency injection
$sanitizer = new Sanitization(new Utils());

use SimpleCrud\Database;
$dsn = 'mysql:host=localhost;dbname=social';
$username = 'root';
$password = '';
$pdo = new PDO($dsn, $username, $password);

$db = new Database($pdo);

//To get any table, use magic properties, they will be instantiated on demand:

?>