<?php

require_once '../includes/config.php';

//using simple crud fetch videos from db and send in card like interface
if(isset($_POST['search'])){
    $search = '%' . $_POST['search'] . '%';
    $stmt = $pdo->prepare("SELECT * FROM video WHERE title LIKE :search ORDER BY id ASC LIMIT 10");
    $stmt->bindParam(':search', $search, PDO::PARAM_STR);
    $stmt->execute();
    $videos = $stmt->fetchAll(PDO::FETCH_OBJ);
}else{
    $videos = $db->video->select()    
        ->orderBy('id ASC')
        ->limit(10)
        ->get();
}

foreach ($videos as $video) {
    // Format view count
    $viewCount = $video->views;
    
    if ($viewCount >= 1000000) {
        $formattedViews = number_format($viewCount/1000000, 1) . 'M';
    } else if ($viewCount >= 1000) {
        $formattedViews = number_format($viewCount/1000, 1) . 'K';
    } else {
        $formattedViews = $viewCount;
    }

    // Format upload time
    $uploadDate = $video->upload;
    $now = new DateTime();
    $interval = $now->diff($uploadDate);
    
    if ($interval->y > 0) {
        $timeAgo = $interval->y . ' year' . ($interval->y > 1 ? 's' : '') . ' ago';
    } elseif ($interval->m > 0) {
        $timeAgo = $interval->m . ' month' . ($interval->m > 1 ? 's' : '') . ' ago';
    } elseif ($interval->d > 6) {
        $weeks = floor($interval->d / 7);
        $timeAgo = $weeks . ' week' . ($weeks > 1 ? 's' : '') . ' ago';
    } elseif ($interval->d > 0) {
        $timeAgo = $interval->d . ' day' . ($interval->d > 1 ? 's' : '') . ' ago';
    }else {
        $timeAgo = 'less than a day ago';
    }
    $user = $db->user[$video->uid];
    ?>
      <div class="bg-white rounded-lg shadow-sm hover:shadow-md transition cursor-pointer" onclick="window.location.href='watch?id=<?php echo $video->id;?>'">
                <div class="relative">
                    <img src="<?php echo $video->thumbnailUrl;?>" alt="Video thumbnail" class="w-full h-48 object-cover rounded-t-lg">
                    <div class="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                        <?php echo $video->duration;?>
                    </div>
                </div>
                <div class="p-4">
                    <h3 class="font-semibold text-gray-900 mb-2 line-clamp-2"><?php echo $video->title;?></h3>
                    <p class="text-sm text-gray-600 mb-1"><?php echo $user->name;?></p>
                    <p class="text-sm text-gray-500"><?php echo $formattedViews;?> views • <?php echo $timeAgo;?></p>
                </div>
            </div>
    <?php
}
?>
 <!-- Video Card 1 -->
          
