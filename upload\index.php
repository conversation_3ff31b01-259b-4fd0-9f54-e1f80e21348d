<?php
require_once '../includes/config.php';
$title = 'Upload Video - IslamTube';
include '../includes/header.php';
?>
<body class="bg-gray-50">
    <?php
    $upload = true;
    ?>
    <?php include '../includes/navbar.php'; ?>

    <!-- Main Content -->
    <main class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="bg-white rounded-lg shadow-sm p-8">
            <div class="text-center mb-8">
                <i class="fas fa-cloud-upload-alt text-green-600 text-4xl mb-4"></i>
                <h1 class="text-2xl font-bold text-gray-900 mb-2">Upload Islamic Video</h1>
                <p class="text-gray-600">Share beneficial Islamic content with the Ummah</p>
            </div>

            <form class="space-y-8">
                <!-- Video Upload -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-4">Video File</label>
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-green-400 transition">
                        <i class="fas fa-video text-gray-400 text-3xl mb-4"></i>
                        <p class="text-gray-600 mb-2">Drag and drop your video file here, or</p>
                        <button type="button" class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition">
                            Choose File
                        </button>
                        <p class="text-xs text-gray-500 mt-2">Supported formats: MP4, MOV, AVI (Max 2GB)</p>
                    </div>
                </div>

                <!-- Video Details -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div class="space-y-6">
                        <div>
                            <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                                Video Title *
                            </label>
                            <input type="text" id="title" name="title" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                                   placeholder="Enter video title">
                        </div>

                        <div>
                            <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                                Description
                            </label>
                            <textarea id="description" name="description" rows="6"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 resize-none"
                                      placeholder="Describe your video content..."></textarea>
                        </div>

                        <div>
                            <label for="category" class="block text-sm font-medium text-gray-700 mb-2">
                                Category *
                            </label>
                            <select id="category" name="category" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500">
                                <option value="">Select a category</option>
                                <option value="quran">Quran Recitation</option>
                                <option value="lectures">Islamic Lectures</option>
                                <option value="hadith">Hadith Studies</option>
                                <option value="history">Islamic History</option>
                                <option value="nasheed">Nasheed</option>
                                <option value="dua">Duas & Dhikr</option>
                                <option value="education">Islamic Education</option>
                                <option value="other">Other</option>
                            </select>
                        </div>

                        <div>
                            <label for="tags" class="block text-sm font-medium text-gray-700 mb-2">
                                Tags
                            </label>
                            <input type="text" id="tags" name="tags"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                                   placeholder="islam, quran, hadith (separate with commas)">
                        </div>
                    </div>

                    <div class="space-y-6">
                        <!-- Thumbnail Upload -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Thumbnail</label>
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                                <i class="fas fa-image text-gray-400 text-2xl mb-2"></i>
                                <p class="text-sm text-gray-600 mb-2">Upload custom thumbnail</p>
                                <button type="button" class="text-green-600 hover:text-green-700 text-sm font-medium">
                                    Choose Image
                                </button>
                                <p class="text-xs text-gray-500 mt-1">JPG, PNG (Max 2MB)</p>
                            </div>
                        </div>

                        <!-- Privacy Settings -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-3">Privacy</label>
                            <div class="space-y-3">
                                <label class="flex items-center">
                                    <input type="radio" name="privacy" value="public" checked
                                           class="h-4 w-4 text-green-600 focus:ring-green-500">
                                    <span class="ml-3">
                                        <span class="text-sm font-medium text-gray-900">Public</span>
                                        <span class="block text-xs text-gray-500">Anyone can view</span>
                                    </span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="privacy" value="unlisted"
                                           class="h-4 w-4 text-green-600 focus:ring-green-500">
                                    <span class="ml-3">
                                        <span class="text-sm font-medium text-gray-900">Unlisted</span>
                                        <span class="block text-xs text-gray-500">Only people with link can view</span>
                                    </span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="privacy" value="private"
                                           class="h-4 w-4 text-green-600 focus:ring-green-500">
                                    <span class="ml-3">
                                        <span class="text-sm font-medium text-gray-900">Private</span>
                                        <span class="block text-xs text-gray-500">Only you can view</span>
                                    </span>
                                </label>
                            </div>
                        </div>

                        <!-- Additional Options -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-3">Additional Options</label>
                            <div class="space-y-3">
                                <label class="flex items-center">
                                    <input type="checkbox" name="comments" checked
                                           class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded">
                                    <span class="ml-3 text-sm text-gray-900">Allow comments</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="likes" checked
                                           class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded">
                                    <span class="ml-3 text-sm text-gray-900">Allow likes/dislikes</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="notifications"
                                           class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded">
                                    <span class="ml-3 text-sm text-gray-900">Notify subscribers</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Islamic Content Guidelines -->
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="flex items-start">
                        <i class="fas fa-info-circle text-green-600 mt-0.5 mr-3"></i>
                        <div>
                            <h3 class="text-sm font-medium text-green-800 mb-2">Islamic Content Guidelines</h3>
                            <ul class="text-sm text-green-700 space-y-1">
                                <li>• Content should be beneficial and in accordance with Islamic teachings</li>
                                <li>• Avoid content that contradicts Islamic values</li>
                                <li>• Ensure proper Islamic etiquette and respect</li>
                                <li>• Verify authenticity of religious information shared</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-between items-center pt-6 border-t">
                    <button type="button" class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition">
                        Save as Draft
                    </button>
                    <div class="space-x-4">
                        <button type="button" class="px-6 py-2 text-gray-600 hover:text-gray-900">
                            Cancel
                        </button>
                        <button type="submit" class="bg-green-600 text-white px-8 py-2 rounded-lg hover:bg-green-700 transition">
                            Upload Video
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </main>
</body>
</html>