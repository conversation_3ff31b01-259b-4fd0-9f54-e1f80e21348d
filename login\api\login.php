<?php
include '../../includes/config.php';

$email = $_POST['email'];
$password = $_POST['password'];
//sanitize use phpSanitination Library
$emailGood = $sanitizer->validateEmail($email, [], false);

if($emailGood){
    $user = $db->user->get(['email' => $email]);
    if ($user) {
        if (password_verify($password, $user->password)) {
            $_SESSION['userEmail'] = $email;
            $_SESSION['userName'] = $user->name;
            $_SESSION['userId'] = $user->id;
            echo "You have logged in successfully. <script>window.location.href='../'</script>";
            exit;
        }else{
            echo "Invalid password";
            exit;
        }
    }
}else{
    echo "Invalid email";
    exit;
}

?>