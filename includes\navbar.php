 <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-4">
                    <a href="/social" class="flex items-center space-x-2">
                        <i class="fas fa-moon text-green-600 text-2xl"></i>
                        <h1 class="text-xl font-bold text-gray-900">IslamTube</h1>
                    </a>
                </div>
                <?php
                
                if (isset($upload)) {
                    ?>

                    <?php
                }else{
                ?>
                <div class="flex-1 max-w-2xl mx-8">
                    <div class="relative">
                        <input hx-post="/social/mainApis/fetchVideos.php" hx-trigger="keyup[key=='Enter']" hx-target="#videosArea" type="text" name="search" id="search" placeholder="Search for videos..." class="w-full px-4 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-green-500">
                        <button class="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <?php }?>
                <div class="flex items-center space-x-4">
                    <a href="upload" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition">
                        <i class="fas fa-plus mr-2"></i>Upload
                    </a>
                    <a href="<?php echo $mainUrl;?>account" class="text-gray-600 hover:text-gray-900">
                        <i class="fas fa-user-circle text-2xl"></i>
                    </a>
                    <?php
                    if (isset($_SESSION['userEmail'])) {
                        ?>
                        <a href="<?php echo $mainUrl;?>mainApis/logout.php" class="text-green-600 hover:text-green-700 font-medium">Logout</a>
                    <?php
                    }else{
                    ?>
                        <a href="<?php echo $mainUrl;?>login" class="text-green-600 hover:text-green-700 font-medium">Login</a>
                    <?php
                    }
                    ?>  
                </div>
            </div>
        </div>
    </header>
